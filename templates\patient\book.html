{% extends "base.html" %}

{% block title %}Book Appointment - Doctor Appointment App{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="bi bi-plus-circle me-2"></i>Book New Appointment
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Note:</strong> You can only book one appointment per day. 
                        Please choose your preferred date and available time slot.
                    </div>

                    <form method="POST" id="bookingForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="appointment_date" class="form-label">
                                    <i class="bi bi-calendar me-1"></i> Appointment Date
                                </label>
                                <input type="date" class="form-control" id="appointment_date" 
                                       name="appointment_date" required>
                                <div class="form-text">Select a date from today onwards</div>
                                <div class="invalid-feedback">
                                    Please select a valid date.
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="appointment_time" class="form-label">
                                    <i class="bi bi-clock me-1"></i> Appointment Time
                                </label>
                                <select class="form-select" id="appointment_time" name="appointment_time" 
                                        required disabled>
                                    <option value="">First select a date</option>
                                </select>
                                <div class="form-text">Available time slots will appear after selecting a date</div>
                                <div class="invalid-feedback">
                                    Please select an available time slot.
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="reason" class="form-label">
                                <i class="bi bi-chat-text me-1"></i> Reason for Visit
                            </label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" 
                                      placeholder="Please describe the reason for your appointment (optional)"></textarea>
                            <div class="form-text">This helps the doctor prepare for your visit</div>
                        </div>

                        <div class="mb-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="bi bi-clock me-1"></i> Available Time Slots
                                    </h6>
                                    <p class="card-text text-muted mb-2">
                                        Morning: 9:00 AM - 12:00 PM<br>
                                        Afternoon: 2:00 PM - 5:00 PM
                                    </p>
                                    <small class="text-muted">
                                        Each appointment is scheduled for 30 minutes
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('patient.dashboard') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="bi bi-arrow-left me-1"></i> Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                <i class="bi bi-check-circle me-1"></i> Book Appointment
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Appointment Guidelines -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-info-circle text-primary me-2"></i>Appointment Guidelines
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Arrive 15 minutes early
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Bring a valid ID
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Bring insurance card (if applicable)
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    List of current medications
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Cancel at least 24 hours in advance
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    One appointment per day limit
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('appointment_date');
    const timeSelect = document.getElementById('appointment_time');
    const submitBtn = document.getElementById('submitBtn');
    const form = document.getElementById('bookingForm');

    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    dateInput.min = today;

    // Date change handler
    dateInput.addEventListener('change', async function() {
        const selectedDate = this.value;
        
        if (selectedDate) {
            timeSelect.innerHTML = '<option value="">Loading...</option>';
            timeSelect.disabled = true;
            submitBtn.disabled = true;

            try {
                const slotsData = await AppointmentBooking.getAvailableSlots(selectedDate);
                
                if (slotsData && slotsData.available_slots) {
                    AppointmentBooking.updateTimeSlots(slotsData.available_slots);
                    
                    if (slotsData.available_slots.length === 0) {
                        AppUtils.showAlert('No available slots for the selected date. Please choose another date.', 'warning');
                    }
                } else {
                    timeSelect.innerHTML = '<option value="">No slots available</option>';
                    timeSelect.disabled = true;
                }
            } catch (error) {
                timeSelect.innerHTML = '<option value="">Error loading slots</option>';
                timeSelect.disabled = true;
                AppUtils.showAlert('Failed to load available time slots. Please try again.', 'danger');
            }
        } else {
            timeSelect.innerHTML = '<option value="">First select a date</option>';
            timeSelect.disabled = true;
            submitBtn.disabled = true;
        }
    });

    // Time selection handler
    timeSelect.addEventListener('change', function() {
        submitBtn.disabled = !this.value;
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        const date = dateInput.value;
        const time = timeSelect.value;

        if (!date || !time) {
            e.preventDefault();
            AppUtils.showAlert('Please select both date and time for your appointment.', 'warning');
            return;
        }

        // Check if date is not in the past
        if (date < today) {
            e.preventDefault();
            AppUtils.showAlert('Cannot book appointments in the past.', 'danger');
            return;
        }

        AppUtils.showLoading();
    });

    // Real-time validation
    [dateInput, timeSelect].forEach(input => {
        input.addEventListener('change', function() {
            if (this.value) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
});
</script>
{% endblock %}
