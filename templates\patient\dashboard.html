{% extends "base.html" %}

{% block title %}Dashboard - Doctor Appointment App{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 fw-bold">Welcome, {{ current_user.name }}!</h1>
                    <p class="text-muted">Manage your appointments and health information</p>
                </div>
                <div>
                    <a href="{{ url_for('patient.book_appointment') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i> Book Appointment
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card dashboard-card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-check display-6 text-primary mb-2"></i>
                    <h5 class="card-title">{{ upcoming_appointments|length }}</h5>
                    <p class="card-text text-muted">Upcoming Appointments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card success h-100">
                <div class="card-body text-center">
                    <i class="bi bi-check-circle display-6 text-success mb-2"></i>
                    <h5 class="card-title">{{ recent_appointments|length }}</h5>
                    <p class="card-text text-muted">Recent Visits</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card info h-100">
                <div class="card-body text-center">
                    <i class="bi bi-person display-6 text-info mb-2"></i>
                    <h5 class="card-title">{{ patient.age or 'N/A' }}</h5>
                    <p class="card-text text-muted">Age</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card warning h-100">
                <div class="card-body text-center">
                    <i class="bi bi-telephone display-6 text-warning mb-2"></i>
                    <h5 class="card-title">{{ patient.phone or 'Not Set' }}</h5>
                    <p class="card-text text-muted">Contact</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Appointments -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-event me-2"></i>Upcoming Appointments
                    </h5>
                </div>
                <div class="card-body">
                    {% if upcoming_appointments %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Time</th>
                                        <th>Reason</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for appointment in upcoming_appointments %}
                                    <tr>
                                        <td>{{ appointment.appointment_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ appointment.appointment_time }}</td>
                                        <td>{{ appointment.reason or 'General consultation' }}</td>
                                        <td>
                                            <span class="badge status-{{ appointment.status }}">
                                                {{ appointment.status.title() }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if appointment.status == 'scheduled' %}
                                            <form method="POST" action="{{ url_for('patient.cancel_appointment', appointment_id=appointment.id) }}" class="d-inline">
                                                <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                        data-confirm="Are you sure you want to cancel this appointment?">
                                                    <i class="bi bi-x-circle"></i> Cancel
                                                </button>
                                            </form>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-calendar-x display-4 text-muted mb-3"></i>
                            <h5>No upcoming appointments</h5>
                            <p class="text-muted">Book your first appointment to get started</p>
                            <a href="{{ url_for('patient.book_appointment') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-1"></i> Book Now
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Actions & Recent History -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('patient.book_appointment') }}" class="btn btn-outline-primary">
                            <i class="bi bi-plus-circle me-2"></i>Book Appointment
                        </a>
                        <a href="{{ url_for('patient.appointments') }}" class="btn btn-outline-info">
                            <i class="bi bi-calendar-event me-2"></i>View All Appointments
                        </a>
                        <a href="{{ url_for('patient.profile') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-person me-2"></i>Update Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent History -->
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>Recent History
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_appointments %}
                        {% for appointment in recent_appointments %}
                        <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                            <div>
                                <small class="fw-bold">{{ appointment.appointment_date.strftime('%m/%d') }}</small>
                                <br>
                                <small class="text-muted">{{ appointment.appointment_time }}</small>
                            </div>
                            <span class="badge status-{{ appointment.status }}">
                                {{ appointment.status.title() }}
                            </span>
                        </div>
                        {% endfor %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('patient.appointments') }}" class="btn btn-sm btn-outline-primary">
                                View All
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No appointment history</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add current date for comparison
    window.today = new Date().toISOString().split('T')[0];
    
    // Auto-refresh upcoming appointments every 5 minutes
    setInterval(function() {
        location.reload();
    }, 300000);
});
</script>
{% endblock %}
