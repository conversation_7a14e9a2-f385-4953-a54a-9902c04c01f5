from flask import Blueprint, render_template, redirect, url_for
from flask_login import current_user

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    if current_user.is_authenticated:
        if current_user.is_admin:
            return redirect(url_for('admin.dashboard'))
        else:
            return redirect(url_for('patient.dashboard'))
    return render_template('index.html')

@main_bp.route('/manifest.json')
def manifest():
    from flask import jsonify, current_app
    return jsonify({
        "name": current_app.config['PWA_NAME'],
        "short_name": current_app.config['PWA_SHORT_NAME'],
        "description": current_app.config['PWA_DESCRIPTION'],
        "start_url": "/",
        "display": "standalone",
        "background_color": "#ffffff",
        "theme_color": "#007bff",
        "icons": [
            {
                "src": "/static/images/icon-192x192.png",
                "sizes": "192x192",
                "type": "image/png"
            },
            {
                "src": "/static/images/icon-512x512.png",
                "sizes": "512x512",
                "type": "image/png"
            }
        ]
    })

@main_bp.route('/sw.js')
def service_worker():
    from flask import Response
    sw_content = '''
const CACHE_NAME = 'doctor-appointment-v1';
const urlsToCache = [
    '/',
    '/static/css/style.css',
    '/static/js/app.js',
    '/static/images/icon-192x192.png',
    '/static/images/icon-512x512.png'
];

self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});
'''
    return Response(sw_content, mimetype='application/javascript')

@main_bp.route('/offline')
def offline():
    return render_template('offline.html')
