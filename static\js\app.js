// Doctor Appointment App - Main JavaScript

// PWA Installation
let deferredPrompt;
const installBtn = document.getElementById('installBtn');

// Service Worker Registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// PWA Install Prompt
window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
    installBtn.style.display = 'inline-block';
});

installBtn.addEventListener('click', async () => {
    if (deferredPrompt) {
        deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        console.log(`User response to the install prompt: ${outcome}`);
        deferredPrompt = null;
        installBtn.style.display = 'none';
    }
});

// Hide install button if app is already installed
window.addEventListener('appinstalled', () => {
    installBtn.style.display = 'none';
    deferredPrompt = null;
});

// Utility Functions
class AppUtils {
    static showLoading() {
        const overlay = document.createElement('div');
        overlay.className = 'spinner-overlay';
        overlay.id = 'loadingOverlay';
        overlay.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        `;
        document.body.appendChild(overlay);
    }

    static hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.remove();
        }
    }

    static showAlert(message, type = 'info') {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container');
        if (container) {
            container.insertBefore(alertContainer, container.firstChild);
        }
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertContainer.parentNode) {
                alertContainer.remove();
            }
        }, 5000);
    }

    static formatDate(dateString) {
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    }

    static formatTime(timeString) {
        const [hours, minutes] = timeString.split(':');
        const time = new Date();
        time.setHours(parseInt(hours), parseInt(minutes));
        return time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
}

// Appointment Booking Functions
class AppointmentBooking {
    static async getAvailableSlots(date) {
        try {
            AppUtils.showLoading();
            const response = await fetch(`/api/available-slots?date=${date}`);
            const data = await response.json();
            AppUtils.hideLoading();
            
            if (response.ok) {
                return data;
            } else {
                AppUtils.showAlert(data.error || 'Failed to fetch available slots', 'danger');
                return null;
            }
        } catch (error) {
            AppUtils.hideLoading();
            AppUtils.showAlert('Network error. Please try again.', 'danger');
            return null;
        }
    }

    static updateTimeSlots(slots) {
        const timeSelect = document.getElementById('appointment_time');
        if (!timeSelect) return;

        timeSelect.innerHTML = '<option value="">Select a time</option>';
        
        if (slots && slots.length > 0) {
            slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot;
                option.textContent = AppUtils.formatTime(slot);
                timeSelect.appendChild(option);
            });
            timeSelect.disabled = false;
        } else {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'No available slots';
            timeSelect.appendChild(option);
            timeSelect.disabled = true;
        }
    }
}

// Form Validation
class FormValidator {
    static validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    static validatePhone(phone) {
        const re = /^[\+]?[1-9][\d]{0,15}$/;
        return re.test(phone.replace(/\s/g, ''));
    }

    static validatePassword(password) {
        return password.length >= 6;
    }

    static validateForm(formId) {
        const form = document.getElementById(formId);
        if (!form) return false;

        let isValid = true;
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');

        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        });

        return isValid;
    }
}

// Dashboard Functions
class Dashboard {
    static async loadStats() {
        try {
            const response = await fetch('/api/appointment-stats');
            if (response.ok) {
                const data = await response.json();
                this.updateStatsDisplay(data);
            }
        } catch (error) {
            console.error('Failed to load stats:', error);
        }
    }

    static updateStatsDisplay(data) {
        // Update today's stats
        const todayTotal = document.getElementById('todayTotal');
        const todayScheduled = document.getElementById('todayScheduled');
        const todayCompleted = document.getElementById('todayCompleted');

        if (todayTotal) todayTotal.textContent = data.today.total;
        if (todayScheduled) todayScheduled.textContent = data.today.scheduled;
        if (todayCompleted) todayCompleted.textContent = data.today.completed;
    }
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Date picker for appointment booking
    const dateInput = document.getElementById('appointment_date');
    if (dateInput) {
        // Set minimum date to today
        const today = new Date().toISOString().split('T')[0];
        dateInput.min = today;

        dateInput.addEventListener('change', async function() {
            const selectedDate = this.value;
            if (selectedDate) {
                const slotsData = await AppointmentBooking.getAvailableSlots(selectedDate);
                if (slotsData) {
                    AppointmentBooking.updateTimeSlots(slotsData.available_slots);
                }
            }
        });
    }

    // Form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!FormValidator.validateForm(this.id)) {
                e.preventDefault();
                AppUtils.showAlert('Please fill in all required fields correctly.', 'warning');
            }
        });
    });

    // Real-time form validation
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required')) {
                if (!this.value.trim()) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                } else {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            }
        });
    });

    // Confirmation dialogs for dangerous actions
    const dangerButtons = document.querySelectorAll('[data-confirm]');
    dangerButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm');
            if (!confirm(message)) {
                e.preventDefault();
            }
        });
    });

    // Auto-refresh for admin dashboard
    if (window.location.pathname.includes('/admin/dashboard')) {
        Dashboard.loadStats();
        setInterval(() => {
            Dashboard.loadStats();
        }, 30000); // Refresh every 30 seconds
    }

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });

    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Offline detection
window.addEventListener('online', () => {
    AppUtils.showAlert('You are back online!', 'success');
});

window.addEventListener('offline', () => {
    AppUtils.showAlert('You are offline. Some features may not work.', 'warning');
});

// Export utilities for use in other scripts
window.AppUtils = AppUtils;
window.AppointmentBooking = AppointmentBooking;
window.FormValidator = FormValidator;
window.Dashboard = Dashboard;
