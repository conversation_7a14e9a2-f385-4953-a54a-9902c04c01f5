import os
from datetime import timedelta

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///clinic.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # Upload configuration
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # Clinic configuration
    CLINIC_NAME = "HealthCare Clinic"
    CLINIC_ADDRESS = "123 Medical Street, Health City"
    CLINIC_PHONE = "+****************"
    CLINIC_EMAIL = "<EMAIL>"
    
    # Appointment configuration
    APPOINTMENT_SLOTS = [
        "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
        "14:00", "14:30", "15:00", "15:30", "16:00", "16:30"
    ]
    
    # PWA configuration
    PWA_NAME = "Doctor Appointment"
    PWA_SHORT_NAME = "DocAppt"
    PWA_DESCRIPTION = "Book and manage your doctor appointments"
