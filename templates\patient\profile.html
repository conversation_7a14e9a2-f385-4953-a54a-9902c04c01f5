{% extends "base.html" %}

{% block title %}Profile - Doctor Appointment App{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="bi bi-person me-2"></i>My Profile
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST" id="profileForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="name" value="{{ current_user.name }}" readonly>
                                <div class="form-text">Contact support to change your name</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" value="{{ current_user.email }}" readonly>
                                <div class="form-text">Contact support to change your email</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ patient.phone or '' }}" placeholder="+****************">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="date_of_birth" class="form-label">Date of Birth</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                       value="{{ patient.date_of_birth.strftime('%Y-%m-%d') if patient.date_of_birth else '' }}">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="gender" class="form-label">Gender</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">Select Gender</option>
                                    <option value="male" {{ 'selected' if patient.gender == 'male' else '' }}>Male</option>
                                    <option value="female" {{ 'selected' if patient.gender == 'female' else '' }}>Female</option>
                                    <option value="other" {{ 'selected' if patient.gender == 'other' else '' }}>Other</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Age</label>
                                <input type="text" class="form-control" value="{{ patient.age or 'Not calculated' }}" readonly>
                                <div class="form-text">Calculated from date of birth</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3" 
                                      placeholder="Enter your full address">{{ patient.address or '' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="emergency_contact" class="form-label">Emergency Contact Name</label>
                                <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" 
                                       value="{{ patient.emergency_contact or '' }}" placeholder="Contact person name">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="emergency_phone" class="form-label">Emergency Contact Phone</label>
                                <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" 
                                       value="{{ patient.emergency_phone or '' }}" placeholder="+****************">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="medical_history" class="form-label">Medical History</label>
                            <textarea class="form-control" id="medical_history" name="medical_history" rows="4" 
                                      placeholder="Please describe any relevant medical history, allergies, current medications, etc.">{{ patient.medical_history or '' }}</textarea>
                            <div class="form-text">This information helps your doctor provide better care</div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('patient.dashboard') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="bi bi-arrow-left me-1"></i> Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i> Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Account Information -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-info-circle text-primary me-2"></i>Account Information
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Member Since:</strong> {{ patient.created_at.strftime('%B %d, %Y') }}</p>
                            <p><strong>Last Updated:</strong> {{ patient.updated_at.strftime('%B %d, %Y') if patient.updated_at else 'Never' }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Account Type:</strong> Patient</p>
                            <p><strong>Status:</strong> <span class="badge bg-success">Active</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('profileForm');
    
    // Form submission
    form.addEventListener('submit', function(e) {
        const phone = document.getElementById('phone').value.trim();
        const emergencyPhone = document.getElementById('emergency_phone').value.trim();
        
        // Validate phone numbers if provided
        if (phone && !FormValidator.validatePhone(phone)) {
            e.preventDefault();
            document.getElementById('phone').classList.add('is-invalid');
            AppUtils.showAlert('Please enter a valid phone number.', 'danger');
            return;
        }
        
        if (emergencyPhone && !FormValidator.validatePhone(emergencyPhone)) {
            e.preventDefault();
            document.getElementById('emergency_phone').classList.add('is-invalid');
            AppUtils.showAlert('Please enter a valid emergency contact phone number.', 'danger');
            return;
        }
        
        AppUtils.showLoading();
    });
    
    // Real-time validation for phone numbers
    ['phone', 'emergency_phone'].forEach(fieldId => {
        const field = document.getElementById(fieldId);
        field.addEventListener('blur', function() {
            if (this.value.trim() && !FormValidator.validatePhone(this.value.trim())) {
                this.classList.add('is-invalid');
                this.classList.remove('is-valid');
            } else if (this.value.trim()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-invalid', 'is-valid');
            }
        });
    });
});
</script>
{% endblock %}
