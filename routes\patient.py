from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from extensions import db
from models import Patient, Appointment, AppointmentHistory
from config import Config

patient_bp = Blueprint('patient', __name__)

def patient_required(f):
    """Decorator to ensure user is a patient (not admin)"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if current_user.is_admin:
            flash('Access denied. Patients only.', 'error')
            return redirect(url_for('admin.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

@patient_bp.route('/dashboard')
@login_required
@patient_required
def dashboard():
    patient = Patient.query.filter_by(user_id=current_user.id).first()
    if not patient:
        flash('Patient profile not found. Please contact support.', 'error')
        return redirect(url_for('main.index'))
    
    # Get upcoming appointments
    upcoming_appointments = Appointment.query.filter(
        Appointment.patient_id == patient.id,
        Appointment.appointment_date >= date.today(),
        Appointment.status == 'scheduled'
    ).order_by(Appointment.appointment_date, Appointment.appointment_time).all()
    
    # Get recent appointments
    recent_appointments = Appointment.query.filter(
        Appointment.patient_id == patient.id,
        Appointment.appointment_date < date.today()
    ).order_by(Appointment.appointment_date.desc(), Appointment.appointment_time.desc()).limit(5).all()
    
    return render_template('patient/dashboard.html', 
                         patient=patient, 
                         upcoming_appointments=upcoming_appointments,
                         recent_appointments=recent_appointments)

@patient_bp.route('/book', methods=['GET', 'POST'])
@login_required
@patient_required
def book_appointment():
    patient = Patient.query.filter_by(user_id=current_user.id).first()
    
    if request.method == 'POST':
        appointment_date_str = request.form.get('appointment_date')
        appointment_time = request.form.get('appointment_time')
        reason = request.form.get('reason')
        
        try:
            appointment_date = datetime.strptime(appointment_date_str, '%Y-%m-%d').date()
        except ValueError:
            flash('Invalid date format', 'error')
            return render_template('patient/book.html')
        
        # Validation
        if appointment_date < date.today():
            flash('Cannot book appointments in the past', 'error')
            return render_template('patient/book.html')
        
        # Check if patient already has appointment on this date
        if Appointment.patient_has_appointment_on_date(patient.id, appointment_date):
            flash('You already have an appointment on this date', 'error')
            return render_template('patient/book.html')
        
        # Check if time slot is available
        available_slots = Appointment.get_available_slots(appointment_date)
        if appointment_time not in available_slots:
            flash('Selected time slot is not available', 'error')
            return render_template('patient/book.html')
        
        # Create appointment
        try:
            appointment = Appointment(
                patient_id=patient.id,
                appointment_date=appointment_date,
                appointment_time=appointment_time,
                reason=reason
            )
            db.session.add(appointment)
            db.session.flush()
            
            # Add to history
            history = AppointmentHistory(
                appointment_id=appointment.id,
                action='created',
                new_status='scheduled',
                changed_by=current_user.id
            )
            db.session.add(history)
            db.session.commit()
            
            flash('Appointment booked successfully!', 'success')
            return redirect(url_for('patient.dashboard'))
        except Exception as e:
            db.session.rollback()
            flash('Failed to book appointment. Please try again.', 'error')
    
    return render_template('patient/book.html')

@patient_bp.route('/cancel/<int:appointment_id>', methods=['POST'])
@login_required
@patient_required
def cancel_appointment(appointment_id):
    patient = Patient.query.filter_by(user_id=current_user.id).first()
    appointment = Appointment.query.filter_by(id=appointment_id, patient_id=patient.id).first()
    
    if not appointment:
        flash('Appointment not found', 'error')
        return redirect(url_for('patient.dashboard'))
    
    if appointment.status != 'scheduled':
        flash('Cannot cancel this appointment', 'error')
        return redirect(url_for('patient.dashboard'))
    
    if appointment.appointment_date < date.today():
        flash('Cannot cancel past appointments', 'error')
        return redirect(url_for('patient.dashboard'))
    
    try:
        old_status = appointment.status
        appointment.status = 'cancelled'
        appointment.updated_at = datetime.utcnow()
        
        # Add to history
        history = AppointmentHistory(
            appointment_id=appointment.id,
            action='cancelled',
            old_status=old_status,
            new_status='cancelled',
            changed_by=current_user.id
        )
        db.session.add(history)
        db.session.commit()
        
        flash('Appointment cancelled successfully', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Failed to cancel appointment', 'error')
    
    return redirect(url_for('patient.dashboard'))

@patient_bp.route('/profile', methods=['GET', 'POST'])
@login_required
@patient_required
def profile():
    patient = Patient.query.filter_by(user_id=current_user.id).first()

    if request.method == 'POST':
        # Update patient information
        patient.phone = request.form.get('phone')
        patient.address = request.form.get('address')
        patient.emergency_contact = request.form.get('emergency_contact')
        patient.emergency_phone = request.form.get('emergency_phone')
        patient.medical_history = request.form.get('medical_history')

        dob_str = request.form.get('date_of_birth')
        if dob_str:
            try:
                patient.date_of_birth = datetime.strptime(dob_str, '%Y-%m-%d').date()
            except ValueError:
                flash('Invalid date format', 'error')
                return render_template('patient/profile.html', patient=patient)

        patient.gender = request.form.get('gender')
        patient.updated_at = datetime.utcnow()

        try:
            db.session.commit()
            flash('Profile updated successfully', 'success')
        except Exception as e:
            db.session.rollback()
            flash('Failed to update profile', 'error')

    return render_template('patient/profile.html', patient=patient)

@patient_bp.route('/appointments')
@login_required
@patient_required
def appointments():
    patient = Patient.query.filter_by(user_id=current_user.id).first()

    # Get all appointments
    all_appointments = Appointment.query.filter_by(patient_id=patient.id).order_by(
        Appointment.appointment_date.desc(),
        Appointment.appointment_time.desc()
    ).all()

    return render_template('patient/appointments.html', appointments=all_appointments)
