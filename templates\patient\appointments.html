{% extends "base.html" %}

{% block title %}My Appointments - Doctor Appointment App{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 fw-bold">My Appointments</h1>
                    <p class="text-muted">View and manage all your appointments</p>
                </div>
                <div>
                    <a href="{{ url_for('patient.book_appointment') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i> Book New Appointment
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if appointments %}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Reason</th>
                            <th>Status</th>
                            <th>Notes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for appointment in appointments %}
                        <tr>
                            <td>{{ appointment.appointment_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ appointment.appointment_time }}</td>
                            <td>{{ appointment.reason or 'General consultation' }}</td>
                            <td>
                                <span class="badge status-{{ appointment.status }}">
                                    {{ appointment.status.title() }}
                                </span>
                            </td>
                            <td>{{ appointment.notes or '-' }}</td>
                            <td>
                                {% if appointment.status == 'scheduled' %}
                                <form method="POST" action="{{ url_for('patient.cancel_appointment', appointment_id=appointment.id) }}" class="d-inline">
                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                            data-confirm="Are you sure you want to cancel this appointment?">
                                        <i class="bi bi-x-circle"></i> Cancel
                                    </button>
                                </form>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card shadow-sm">
        <div class="card-body text-center py-5">
            <i class="bi bi-calendar-x display-4 text-muted mb-3"></i>
            <h4>No appointments found</h4>
            <p class="text-muted">You haven't booked any appointments yet.</p>
            <a href="{{ url_for('patient.book_appointment') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i> Book Your First Appointment
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
