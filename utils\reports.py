from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from datetime import datetime, date, timedelta
from io import BytesIO
from models import Appointment, Patient, User
from config import Config

def generate_daily_report(report_date):
    """Generate daily appointments report as PDF"""
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    
    story.append(Paragraph(f"{Config.CLINIC_NAME}", title_style))
    story.append(Paragraph(f"Daily Appointments Report - {report_date.strftime('%B %d, %Y')}", styles['Heading2']))
    story.append(Spacer(1, 20))
    
    # Get appointments for the date
    appointments = Appointment.query.filter_by(appointment_date=report_date).order_by(
        Appointment.appointment_time
    ).all()
    
    if not appointments:
        story.append(Paragraph("No appointments scheduled for this date.", styles['Normal']))
    else:
        # Summary
        total = len(appointments)
        scheduled = len([a for a in appointments if a.status == 'scheduled'])
        completed = len([a for a in appointments if a.status == 'completed'])
        cancelled = len([a for a in appointments if a.status == 'cancelled'])
        no_show = len([a for a in appointments if a.status == 'no_show'])
        
        summary_data = [
            ['Total Appointments', str(total)],
            ['Scheduled', str(scheduled)],
            ['Completed', str(completed)],
            ['Cancelled', str(cancelled)],
            ['No Show', str(no_show)]
        ]
        
        summary_table = Table(summary_data, colWidths=[2*inch, 1*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(Paragraph("Summary", styles['Heading3']))
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # Appointments table
        story.append(Paragraph("Appointments", styles['Heading3']))
        
        data = [['Time', 'Patient', 'Phone', 'Reason', 'Status']]
        for apt in appointments:
            data.append([
                apt.appointment_time,
                apt.patient.user.name,
                apt.patient.phone or 'N/A',
                apt.reason or 'N/A',
                apt.status.title()
            ])
        
        table = Table(data, colWidths=[1*inch, 2*inch, 1.5*inch, 2*inch, 1*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
        ]))
        
        story.append(table)
    
    # Footer
    story.append(Spacer(1, 30))
    story.append(Paragraph(f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
    story.append(Paragraph(f"{Config.CLINIC_ADDRESS}", styles['Normal']))
    story.append(Paragraph(f"Phone: {Config.CLINIC_PHONE}", styles['Normal']))
    
    doc.build(story)
    buffer.seek(0)
    return buffer.getvalue()

def generate_monthly_report(year, month):
    """Generate monthly appointments report as PDF"""
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1
    )
    
    month_name = datetime(year, month, 1).strftime('%B %Y')
    story.append(Paragraph(f"{Config.CLINIC_NAME}", title_style))
    story.append(Paragraph(f"Monthly Report - {month_name}", styles['Heading2']))
    story.append(Spacer(1, 20))
    
    # Date range
    start_date = date(year, month, 1)
    if month == 12:
        end_date = date(year + 1, 1, 1) - timedelta(days=1)
    else:
        end_date = date(year, month + 1, 1) - timedelta(days=1)
    
    # Get appointments for the month
    appointments = Appointment.query.filter(
        Appointment.appointment_date >= start_date,
        Appointment.appointment_date <= end_date
    ).order_by(Appointment.appointment_date, Appointment.appointment_time).all()
    
    # Statistics
    total = len(appointments)
    scheduled = len([a for a in appointments if a.status == 'scheduled'])
    completed = len([a for a in appointments if a.status == 'completed'])
    cancelled = len([a for a in appointments if a.status == 'cancelled'])
    no_show = len([a for a in appointments if a.status == 'no_show'])
    
    # Unique patients
    unique_patients = len(set(a.patient_id for a in appointments))
    
    summary_data = [
        ['Total Appointments', str(total)],
        ['Unique Patients', str(unique_patients)],
        ['Completed', str(completed)],
        ['Scheduled', str(scheduled)],
        ['Cancelled', str(cancelled)],
        ['No Show', str(no_show)]
    ]
    
    summary_table = Table(summary_data, colWidths=[2*inch, 1*inch])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    story.append(Paragraph("Monthly Summary", styles['Heading3']))
    story.append(summary_table)
    story.append(Spacer(1, 20))
    
    # Daily breakdown
    story.append(Paragraph("Daily Breakdown", styles['Heading3']))
    
    # Group appointments by date
    daily_stats = {}
    for apt in appointments:
        apt_date = apt.appointment_date
        if apt_date not in daily_stats:
            daily_stats[apt_date] = {'total': 0, 'completed': 0, 'cancelled': 0}
        daily_stats[apt_date]['total'] += 1
        if apt.status == 'completed':
            daily_stats[apt_date]['completed'] += 1
        elif apt.status == 'cancelled':
            daily_stats[apt_date]['cancelled'] += 1
    
    daily_data = [['Date', 'Total', 'Completed', 'Cancelled']]
    for apt_date in sorted(daily_stats.keys()):
        stats = daily_stats[apt_date]
        daily_data.append([
            apt_date.strftime('%Y-%m-%d'),
            str(stats['total']),
            str(stats['completed']),
            str(stats['cancelled'])
        ])
    
    if len(daily_data) > 1:
        daily_table = Table(daily_data, colWidths=[1.5*inch, 1*inch, 1*inch, 1*inch])
        daily_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
        ]))
        story.append(daily_table)
    else:
        story.append(Paragraph("No appointments found for this month.", styles['Normal']))
    
    # Footer
    story.append(Spacer(1, 30))
    story.append(Paragraph(f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
    story.append(Paragraph(f"{Config.CLINIC_ADDRESS}", styles['Normal']))
    story.append(Paragraph(f"Phone: {Config.CLINIC_PHONE}", styles['Normal']))
    
    doc.build(story)
    buffer.seek(0)
    return buffer.getvalue()

def generate_patient_report(patient_id):
    """Generate individual patient report as PDF"""
    patient = Patient.query.get_or_404(patient_id)
    
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1
    )
    
    story.append(Paragraph(f"{Config.CLINIC_NAME}", title_style))
    story.append(Paragraph(f"Patient Report", styles['Heading2']))
    story.append(Spacer(1, 20))
    
    # Patient information
    story.append(Paragraph("Patient Information", styles['Heading3']))
    
    patient_data = [
        ['Name', patient.user.name],
        ['Email', patient.user.email],
        ['Phone', patient.phone or 'N/A'],
        ['Date of Birth', patient.date_of_birth.strftime('%Y-%m-%d') if patient.date_of_birth else 'N/A'],
        ['Age', str(patient.age) if patient.age else 'N/A'],
        ['Gender', patient.gender or 'N/A'],
        ['Address', patient.address or 'N/A'],
        ['Emergency Contact', patient.emergency_contact or 'N/A'],
        ['Emergency Phone', patient.emergency_phone or 'N/A']
    ]
    
    patient_table = Table(patient_data, colWidths=[2*inch, 3*inch])
    patient_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'TOP')
    ]))
    
    story.append(patient_table)
    story.append(Spacer(1, 20))
    
    # Medical history
    if patient.medical_history:
        story.append(Paragraph("Medical History", styles['Heading3']))
        story.append(Paragraph(patient.medical_history, styles['Normal']))
        story.append(Spacer(1, 20))
    
    # Appointments history
    appointments = Appointment.query.filter_by(patient_id=patient_id).order_by(
        Appointment.appointment_date.desc(),
        Appointment.appointment_time.desc()
    ).all()
    
    story.append(Paragraph("Appointment History", styles['Heading3']))
    
    if appointments:
        apt_data = [['Date', 'Time', 'Reason', 'Status', 'Notes']]
        for apt in appointments:
            apt_data.append([
                apt.appointment_date.strftime('%Y-%m-%d'),
                apt.appointment_time,
                apt.reason or 'N/A',
                apt.status.title(),
                apt.notes or 'N/A'
            ])
        
        apt_table = Table(apt_data, colWidths=[1*inch, 0.8*inch, 2*inch, 1*inch, 2*inch])
        apt_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('VALIGN', (0, 0), (-1, -1), 'TOP')
        ]))
        
        story.append(apt_table)
        
        # Statistics
        story.append(Spacer(1, 20))
        story.append(Paragraph("Statistics", styles['Heading3']))
        
        total_apts = len(appointments)
        completed_apts = len([a for a in appointments if a.status == 'completed'])
        cancelled_apts = len([a for a in appointments if a.status == 'cancelled'])
        
        stats_data = [
            ['Total Appointments', str(total_apts)],
            ['Completed', str(completed_apts)],
            ['Cancelled', str(cancelled_apts)],
            ['Patient Since', patient.created_at.strftime('%Y-%m-%d')]
        ]
        
        stats_table = Table(stats_data, colWidths=[2*inch, 1*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(stats_table)
    else:
        story.append(Paragraph("No appointments found for this patient.", styles['Normal']))
    
    # Footer
    story.append(Spacer(1, 30))
    story.append(Paragraph(f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
    story.append(Paragraph(f"{Config.CLINIC_ADDRESS}", styles['Normal']))
    story.append(Paragraph(f"Phone: {Config.CLINIC_PHONE}", styles['Normal']))
    
    doc.build(story)
    buffer.seek(0)
    return buffer.getvalue()
