from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, make_response
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from sqlalchemy import func, and_, or_
from extensions import db
from models import User, Patient, Appointment, AppointmentHistory
from utils.reports import generate_daily_report, generate_monthly_report, generate_patient_report

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    """Decorator to ensure user is an admin"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_admin:
            flash('Access denied. Admins only.', 'error')
            return redirect(url_for('patient.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    today = date.today()
    
    # Statistics
    total_patients = Patient.query.count()
    today_appointments = Appointment.query.filter_by(appointment_date=today).count()
    pending_appointments = Appointment.query.filter(
        Appointment.appointment_date >= today,
        Appointment.status == 'scheduled'
    ).count()
    
    # Recent appointments
    recent_appointments = Appointment.query.filter_by(appointment_date=today).order_by(
        Appointment.appointment_time
    ).all()
    
    # Monthly stats
    month_start = today.replace(day=1)
    monthly_appointments = Appointment.query.filter(
        Appointment.appointment_date >= month_start,
        Appointment.appointment_date <= today
    ).count()
    
    return render_template('admin/dashboard.html',
                         total_patients=total_patients,
                         today_appointments=today_appointments,
                         pending_appointments=pending_appointments,
                         recent_appointments=recent_appointments,
                         monthly_appointments=monthly_appointments)

@admin_bp.route('/appointments')
@login_required
@admin_required
def appointments():
    page = request.args.get('page', 1, type=int)
    date_filter = request.args.get('date')
    status_filter = request.args.get('status')
    
    query = Appointment.query
    
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            query = query.filter(Appointment.appointment_date == filter_date)
        except ValueError:
            pass
    
    if status_filter:
        query = query.filter(Appointment.status == status_filter)
    
    appointments = query.order_by(
        Appointment.appointment_date.desc(),
        Appointment.appointment_time.desc()
    ).paginate(page=page, per_page=20, error_out=False)
    
    return render_template('admin/appointments.html', appointments=appointments)

@admin_bp.route('/patients')
@login_required
@admin_required
def patients():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Patient.query.join(User)
    
    if search:
        query = query.filter(
            or_(
                User.name.contains(search),
                User.email.contains(search),
                Patient.phone.contains(search)
            )
        )
    
    patients = query.order_by(User.name).paginate(page=page, per_page=20, error_out=False)
    
    return render_template('admin/patients.html', patients=patients, search=search)

@admin_bp.route('/patient/<int:patient_id>')
@login_required
@admin_required
def patient_detail(patient_id):
    patient = Patient.query.get_or_404(patient_id)
    
    # Get patient's appointments
    appointments = Appointment.query.filter_by(patient_id=patient_id).order_by(
        Appointment.appointment_date.desc(),
        Appointment.appointment_time.desc()
    ).all()
    
    return render_template('admin/patient_detail.html', patient=patient, appointments=appointments)

@admin_bp.route('/appointment/<int:appointment_id>/update', methods=['POST'])
@login_required
@admin_required
def update_appointment(appointment_id):
    appointment = Appointment.query.get_or_404(appointment_id)
    
    old_status = appointment.status
    new_status = request.form.get('status')
    notes = request.form.get('notes')
    
    if new_status in ['scheduled', 'completed', 'cancelled', 'no_show']:
        appointment.status = new_status
        appointment.notes = notes
        appointment.updated_at = datetime.utcnow()
        
        # Add to history
        history = AppointmentHistory(
            appointment_id=appointment.id,
            action='updated',
            old_status=old_status,
            new_status=new_status,
            changed_by=current_user.id,
            notes=f"Status changed from {old_status} to {new_status}"
        )
        db.session.add(history)
        
        try:
            db.session.commit()
            flash('Appointment updated successfully', 'success')
        except Exception as e:
            db.session.rollback()
            flash('Failed to update appointment', 'error')
    else:
        flash('Invalid status', 'error')
    
    return redirect(url_for('admin.appointments'))

@admin_bp.route('/reports')
@login_required
@admin_required
def reports():
    return render_template('admin/reports.html')

@admin_bp.route('/reports/daily')
@login_required
@admin_required
def daily_report():
    report_date = request.args.get('date')
    if not report_date:
        report_date = date.today().strftime('%Y-%m-%d')
    
    try:
        report_date_obj = datetime.strptime(report_date, '%Y-%m-%d').date()
        pdf_content = generate_daily_report(report_date_obj)
        
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=daily_report_{report_date}.pdf'
        return response
    except Exception as e:
        flash('Failed to generate report', 'error')
        return redirect(url_for('admin.reports'))

@admin_bp.route('/reports/monthly')
@login_required
@admin_required
def monthly_report():
    year = request.args.get('year', date.today().year, type=int)
    month = request.args.get('month', date.today().month, type=int)
    
    try:
        pdf_content = generate_monthly_report(year, month)
        
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=monthly_report_{year}_{month:02d}.pdf'
        return response
    except Exception as e:
        flash('Failed to generate report', 'error')
        return redirect(url_for('admin.reports'))

@admin_bp.route('/reports/patient/<int:patient_id>')
@login_required
@admin_required
def patient_report(patient_id):
    try:
        pdf_content = generate_patient_report(patient_id)
        patient = Patient.query.get_or_404(patient_id)
        
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=patient_report_{patient.user.name.replace(" ", "_")}.pdf'
        return response
    except Exception as e:
        flash('Failed to generate report', 'error')
        return redirect(url_for('admin.patients'))
