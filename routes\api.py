from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from datetime import datetime, date
from models import Appointment, Patient
from config import Config

api_bp = Blueprint('api', __name__)

@api_bp.route('/available-slots')
@login_required
def available_slots():
    """Get available appointment slots for a given date"""
    date_str = request.args.get('date')
    
    if not date_str:
        return jsonify({'error': 'Date parameter is required'}), 400
    
    try:
        appointment_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400
    
    if appointment_date < date.today():
        return jsonify({'error': 'Cannot get slots for past dates'}), 400
    
    # Check if patient already has appointment on this date (for patients only)
    if not current_user.is_admin:
        patient = Patient.query.filter_by(user_id=current_user.id).first()
        if patient and Appointment.patient_has_appointment_on_date(patient.id, appointment_date):
            return jsonify({'error': 'You already have an appointment on this date'}), 400
    
    available_slots = Appointment.get_available_slots(appointment_date)
    
    return jsonify({
        'date': date_str,
        'available_slots': available_slots,
        'total_slots': len(Config.APPOINTMENT_SLOTS),
        'available_count': len(available_slots)
    })

@api_bp.route('/appointment-stats')
@login_required
def appointment_stats():
    """Get appointment statistics (admin only)"""
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403
    
    today = date.today()
    
    # Today's stats
    today_total = Appointment.query.filter_by(appointment_date=today).count()
    today_scheduled = Appointment.query.filter_by(appointment_date=today, status='scheduled').count()
    today_completed = Appointment.query.filter_by(appointment_date=today, status='completed').count()
    today_cancelled = Appointment.query.filter_by(appointment_date=today, status='cancelled').count()
    
    # This week's stats
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)
    
    week_total = Appointment.query.filter(
        Appointment.appointment_date >= week_start,
        Appointment.appointment_date <= week_end
    ).count()
    
    # This month's stats
    month_start = today.replace(day=1)
    month_total = Appointment.query.filter(
        Appointment.appointment_date >= month_start,
        Appointment.appointment_date <= today
    ).count()
    
    return jsonify({
        'today': {
            'total': today_total,
            'scheduled': today_scheduled,
            'completed': today_completed,
            'cancelled': today_cancelled
        },
        'week': {
            'total': week_total
        },
        'month': {
            'total': month_total
        }
    })

@api_bp.route('/patient-appointments')
@login_required
def patient_appointments():
    """Get current patient's appointments"""
    if current_user.is_admin:
        return jsonify({'error': 'Patient access only'}), 403
    
    patient = Patient.query.filter_by(user_id=current_user.id).first()
    if not patient:
        return jsonify({'error': 'Patient profile not found'}), 404
    
    appointments = Appointment.query.filter_by(patient_id=patient.id).order_by(
        Appointment.appointment_date.desc(),
        Appointment.appointment_time.desc()
    ).all()
    
    appointments_data = []
    for apt in appointments:
        appointments_data.append({
            'id': apt.id,
            'date': apt.appointment_date.strftime('%Y-%m-%d'),
            'time': apt.appointment_time,
            'reason': apt.reason,
            'status': apt.status,
            'notes': apt.notes,
            'can_cancel': apt.status == 'scheduled' and apt.appointment_date >= date.today()
        })
    
    return jsonify({
        'appointments': appointments_data,
        'total': len(appointments_data)
    })
