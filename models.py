from extensions import db
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
from sqlalchemy import func

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    is_admin = db.Column(db.Boolean, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.email}>'

class Patient(db.Model):
    id = db.Column(db.Inte<PERSON>, primary_key=True)
    user_id = db.Column(db.Inte<PERSON>, db.<PERSON>ey('user.id'), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    date_of_birth = db.Column(db.Date, nullable=True)
    gender = db.Column(db.String(10), nullable=True)
    address = db.Column(db.Text, nullable=True)
    emergency_contact = db.Column(db.String(100), nullable=True)
    emergency_phone = db.Column(db.String(20), nullable=True)
    medical_history = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    user = db.relationship('User', backref=db.backref('patient', uselist=False))
    appointments = db.relationship('Appointment', backref='patient', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Patient {self.user.name}>'
    
    @property
    def age(self):
        if self.date_of_birth:
            today = date.today()
            return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))
        return None

class Appointment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patient.id'), nullable=False)
    appointment_date = db.Column(db.Date, nullable=False, index=True)
    appointment_time = db.Column(db.String(5), nullable=False)  # Format: "HH:MM"
    reason = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(20), default='scheduled', nullable=False)  # scheduled, completed, cancelled, no_show
    notes = db.Column(db.Text, nullable=True)  # Doctor's notes
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Constraints
    __table_args__ = (
        db.UniqueConstraint('appointment_date', 'appointment_time', name='unique_datetime_slot'),
        db.Index('idx_patient_date', 'patient_id', 'appointment_date'),
    )
    
    def __repr__(self):
        return f'<Appointment {self.patient.user.name} on {self.appointment_date} at {self.appointment_time}>'
    
    @property
    def datetime_str(self):
        return f"{self.appointment_date.strftime('%Y-%m-%d')} {self.appointment_time}"
    
    @classmethod
    def get_available_slots(cls, date_obj):
        """Get available appointment slots for a given date"""
        from config import Config
        booked_times = db.session.query(cls.appointment_time).filter(
            cls.appointment_date == date_obj,
            cls.status.in_(['scheduled'])
        ).all()
        booked_times = [time[0] for time in booked_times]
        
        available_slots = [slot for slot in Config.APPOINTMENT_SLOTS if slot not in booked_times]
        return available_slots
    
    @classmethod
    def patient_has_appointment_on_date(cls, patient_id, date_obj):
        """Check if patient already has an appointment on the given date"""
        return cls.query.filter(
            cls.patient_id == patient_id,
            cls.appointment_date == date_obj,
            cls.status.in_(['scheduled'])
        ).first() is not None

class AppointmentHistory(db.Model):
    """Track changes to appointments for audit purposes"""
    id = db.Column(db.Integer, primary_key=True)
    appointment_id = db.Column(db.Integer, db.ForeignKey('appointment.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # created, updated, cancelled
    old_status = db.Column(db.String(20), nullable=True)
    new_status = db.Column(db.String(20), nullable=True)
    changed_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text, nullable=True)
    
    # Relationships
    appointment = db.relationship('Appointment', backref='history')
    user = db.relationship('User', backref='appointment_changes')
    
    def __repr__(self):
        return f'<AppointmentHistory {self.action} for appointment {self.appointment_id}>'
